from binance.client import Client
import time

API_KEY = "673Zn8T7XYKnrsxwSI8vOd6iMcRQERSNgkvIVXAyp4Imj1bzVX6Co4EypFYfh0kB"
API_SECRET = "U9A260IyIYoi5H8fpah74Y60qLqEnItCapYTEeLOebmqwUXGqMIG0MJwyyozbyQW"

# Connect to Testnet
client = Client(API_KEY, API_SECRET, testnet=True)

# Pick trading pair & settings
symbol = "BTCUSDT"
quantity = 0.001
profit_target = 0.002 

def get_price():
    ticker = client.get_symbol_ticker(symbol=symbol)
    return float(ticker["price"])

def place_order(side, qty, price=None):
    if side == "BUY":
        order = client.order_market_buy(symbol=symbol, quantity=qty)
    elif side == "SELL":
        order = client.order_market_sell(symbol=symbol, quantity=qty)
    print(f"Placed {side} order: {order}")

if __name__ == "__main__":
    entry_price = get_price()
    print(f"Buying at {entry_price} USDT")
    place_order("BUY", quantity)

    while True:
        price = get_price()
        if price >= entry_price * (1 + profit_target):
            print(f"Target reached! Selling at {price} USDT")
            place_order("SELL", quantity)
            break
        print(f"Current price: {price} — Waiting for target...")
        time.sleep(2)
